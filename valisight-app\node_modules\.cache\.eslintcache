[{"C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\layout\\LayOut.jsx": "4", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\EditReportPage.js": "5", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Auth.jsx": "6", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ResetLink.jsx": "7", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ForgotPassword.jsx": "8", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\CheckEmail.jsx": "9", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\Login.jsx": "10", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ResetPassword.jsx": "11", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\faqs\\Faqs.jsx": "12", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Dashboard\\Dashboard.jsx": "13", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\examples\\Examples.jsx": "14", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\View.jsx": "15", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompanies.jsx": "16", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\faq.js": "17", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\report.js": "18", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\auth.js": "19", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\example.js": "20", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\EditReport.jsx": "21", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\TopBar.jsx": "22", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\company.js": "23", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\settings.js": "24", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Companies.jsx": "25", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Create.jsx": "26", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Reports.jsx": "27", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\BreadCrumbs.jsx": "28", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\shared.js": "29", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompaniesList.jsx": "30", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\CSVModal.jsx": "31", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\hooks\\useDebounceSearch.jsx": "32", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Edit.jsx": "33", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompaniesModal.jsx": "34", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\axiosInstance.js": "35", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\cookies.js": "36", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\NoDataFound.jsx": "37", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\markets.js": "38", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ReportModal.jsx": "39", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ShareUsersModal.jsx": "40", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\Table.jsx": "41", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\enums\\report.enum.js": "42", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\CustomizeReport.jsx": "43", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\QboCallback\\qboCallback.jsx": "44", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\pdf.js": "45", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\ReportSummary.jsx": "46", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\ExpenseSummary.jsx": "47", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\FiscalYear.jsx": "48", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\OperationalEfficiency.jsx": "49", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\LiquiditySummary.jsx": "50", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\YearToDate.jsx": "51", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\Monthly.jsx": "52", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\TableOfContents.jsx": "53", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\MonthTrailing.jsx": "54", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\BalanceSheet.jsx": "55", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\CoverPage.jsx": "56", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\pageNumbering.js": "57", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\qbo.js": "58", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\UploadableSection.jsx": "59", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\QuickBooksConnection.jsx": "60", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ReportSettings.jsx": "61", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\contentSettings.js": "62"}, {"size": 507, "mtime": 1756456910065, "results": "63", "hashOfConfig": "64"}, {"size": 2753, "mtime": 1756464224224, "results": "65", "hashOfConfig": "64"}, {"size": 375, "mtime": 1756456910096, "results": "66", "hashOfConfig": "64"}, {"size": 305, "mtime": 1756456910067, "results": "67", "hashOfConfig": "64"}, {"size": 886, "mtime": 1756464224277, "results": "68", "hashOfConfig": "64"}, {"size": 182, "mtime": 1756456910069, "results": "69", "hashOfConfig": "64"}, {"size": 2138, "mtime": 1756464224236, "results": "70", "hashOfConfig": "64"}, {"size": 4322, "mtime": 1756456910072, "results": "71", "hashOfConfig": "64"}, {"size": 2441, "mtime": 1756456910071, "results": "72", "hashOfConfig": "64"}, {"size": 5637, "mtime": 1756464224236, "results": "73", "hashOfConfig": "64"}, {"size": 6189, "mtime": 1756464210205, "results": "74", "hashOfConfig": "64"}, {"size": 2963, "mtime": 1756456910095, "results": "75", "hashOfConfig": "64"}, {"size": 2268, "mtime": 1756456910093, "results": "76", "hashOfConfig": "64"}, {"size": 13394, "mtime": 1756464224274, "results": "77", "hashOfConfig": "64"}, {"size": 45252, "mtime": 1756718264760, "results": "78", "hashOfConfig": "64"}, {"size": 2343, "mtime": 1756464224249, "results": "79", "hashOfConfig": "64"}, {"size": 139, "mtime": 1756456910115, "results": "80", "hashOfConfig": "64"}, {"size": 801, "mtime": 1756456910118, "results": "81", "hashOfConfig": "64"}, {"size": 549, "mtime": 1756456910108, "results": "82", "hashOfConfig": "64"}, {"size": 311, "mtime": 1756456910114, "results": "83", "hashOfConfig": "64"}, {"size": 34297, "mtime": 1756464224241, "results": "84", "hashOfConfig": "64"}, {"size": 3608, "mtime": 1756464224294, "results": "85", "hashOfConfig": "64"}, {"size": 1491, "mtime": 1756456910112, "results": "86", "hashOfConfig": "64"}, {"size": 557, "mtime": 1756463597466, "results": "87", "hashOfConfig": "64"}, {"size": 13482, "mtime": 1756464224237, "results": "88", "hashOfConfig": "64"}, {"size": 26570, "mtime": 1756464224239, "results": "89", "hashOfConfig": "64"}, {"size": 12433, "mtime": 1756468795918, "results": "90", "hashOfConfig": "64"}, {"size": 1201, "mtime": 1756464224292, "results": "91", "hashOfConfig": "64"}, {"size": 688, "mtime": 1756464224299, "results": "92", "hashOfConfig": "64"}, {"size": 4023, "mtime": 1756456910091, "results": "93", "hashOfConfig": "64"}, {"size": 9364, "mtime": 1756456910082, "results": "94", "hashOfConfig": "64"}, {"size": 398, "mtime": 1756456910064, "results": "95", "hashOfConfig": "64"}, {"size": 25971, "mtime": 1756464224240, "results": "96", "hashOfConfig": "64"}, {"size": 5490, "mtime": 1756456910092, "results": "97", "hashOfConfig": "64"}, {"size": 1370, "mtime": 1756464224289, "results": "98", "hashOfConfig": "64"}, {"size": 756, "mtime": 1756464224297, "results": "99", "hashOfConfig": "64"}, {"size": 1596, "mtime": 1756456910125, "results": "100", "hashOfConfig": "64"}, {"size": 703, "mtime": 1756456910117, "results": "101", "hashOfConfig": "64"}, {"size": 18622, "mtime": 1756464224243, "results": "102", "hashOfConfig": "64"}, {"size": 6399, "mtime": 1756456910088, "results": "103", "hashOfConfig": "64"}, {"size": 4226, "mtime": 1756464224293, "results": "104", "hashOfConfig": "64"}, {"size": 191, "mtime": 1756464224235, "results": "105", "hashOfConfig": "64"}, {"size": 93513, "mtime": 1756726685363, "results": "106", "hashOfConfig": "64"}, {"size": 16366, "mtime": 1756464224271, "results": "107", "hashOfConfig": "64"}, {"size": 1331, "mtime": 1756464224290, "results": "108", "hashOfConfig": "64"}, {"size": 16835, "mtime": 1756464224285, "results": "109", "hashOfConfig": "64"}, {"size": 35402, "mtime": 1756720750889, "results": "110", "hashOfConfig": "64"}, {"size": 35016, "mtime": 1756710486552, "results": "111", "hashOfConfig": "64"}, {"size": 31898, "mtime": 1756707769296, "results": "112", "hashOfConfig": "64"}, {"size": 19506, "mtime": 1756716216299, "results": "113", "hashOfConfig": "64"}, {"size": 16880, "mtime": 1756716602567, "results": "114", "hashOfConfig": "64"}, {"size": 18116, "mtime": 1756710570276, "results": "115", "hashOfConfig": "64"}, {"size": 3072, "mtime": 1756464224286, "results": "116", "hashOfConfig": "64"}, {"size": 9630, "mtime": 1756708981945, "results": "117", "hashOfConfig": "64"}, {"size": 20717, "mtime": 1756710764156, "results": "118", "hashOfConfig": "64"}, {"size": 2970, "mtime": 1756464224279, "results": "119", "hashOfConfig": "64"}, {"size": 4323, "mtime": 1756713640371, "results": "120", "hashOfConfig": "64"}, {"size": 1198, "mtime": 1756464224291, "results": "121", "hashOfConfig": "64"}, {"size": 11921, "mtime": 1756467113937, "results": "122", "hashOfConfig": "64"}, {"size": 10932, "mtime": 1756464224242, "results": "123", "hashOfConfig": "64"}, {"size": 18807, "mtime": 1756473098210, "results": "124", "hashOfConfig": "64"}, {"size": 1226, "mtime": 1756464224289, "results": "125", "hashOfConfig": "64"}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cg5eed", {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\layout\\LayOut.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\EditReportPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Auth.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ResetLink.jsx", ["312"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ForgotPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\CheckEmail.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\Login.jsx", ["313"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ResetPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\faqs\\Faqs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Dashboard\\Dashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\examples\\Examples.jsx", ["314"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\View.jsx", ["315", "316", "317", "318"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompanies.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\faq.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\report.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\example.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\EditReport.jsx", ["319", "320", "321", "322", "323", "324", "325", "326", "327", "328"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\TopBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\company.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\settings.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Companies.jsx", ["329", "330"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Create.jsx", ["331", "332"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Reports.jsx", ["333", "334"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\BreadCrumbs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\shared.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompaniesList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\CSVModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\hooks\\useDebounceSearch.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Edit.jsx", ["335", "336", "337", "338"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompaniesModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\axiosInstance.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\cookies.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\NoDataFound.jsx", ["339"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\markets.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ReportModal.jsx", ["340", "341", "342"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ShareUsersModal.jsx", ["343", "344"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\Table.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\enums\\report.enum.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\CustomizeReport.jsx", ["345", "346", "347", "348", "349"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\QboCallback\\qboCallback.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\pdf.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\ReportSummary.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\ExpenseSummary.jsx", ["350", "351", "352"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\FiscalYear.jsx", ["353", "354", "355", "356"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\OperationalEfficiency.jsx", ["357", "358"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\LiquiditySummary.jsx", ["359"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\YearToDate.jsx", ["360"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\Monthly.jsx", ["361"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\TableOfContents.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\MonthTrailing.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\BalanceSheet.jsx", ["362"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\CoverPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\pageNumbering.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\qbo.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\UploadableSection.jsx", ["363", "364", "365", "366", "367", "368", "369"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\QuickBooksConnection.jsx", ["370", "371", "372", "373", "374"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ReportSettings.jsx", ["375", "376", "377", "378", "379", "380", "381", "382", "383", "384", "385"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\contentSettings.js", [], [], {"ruleId": "386", "severity": 1, "message": "387", "line": 2, "column": 10, "nodeType": "388", "messageId": "389", "endLine": 2, "endColumn": 21}, {"ruleId": "386", "severity": 1, "message": "390", "line": 23, "column": 32, "nodeType": "388", "messageId": "389", "endLine": 23, "endColumn": 42}, {"ruleId": "391", "severity": 1, "message": "392", "line": 363, "column": 21, "nodeType": "393", "endLine": 363, "endColumn": 66}, {"ruleId": "386", "severity": 1, "message": "394", "line": 16, "column": 10, "nodeType": "388", "messageId": "389", "endLine": 16, "endColumn": 14}, {"ruleId": "386", "severity": 1, "message": "395", "line": 31, "column": 10, "nodeType": "388", "messageId": "389", "endLine": 31, "endColumn": 19}, {"ruleId": "386", "severity": 1, "message": "396", "line": 31, "column": 21, "nodeType": "388", "messageId": "389", "endLine": 31, "endColumn": 30}, {"ruleId": "386", "severity": 1, "message": "397", "line": 59, "column": 24, "nodeType": "388", "messageId": "389", "endLine": 59, "endColumn": 39}, {"ruleId": "398", "severity": 1, "message": "399", "line": 222, "column": 37, "nodeType": "400", "messageId": "401", "endLine": 232, "endColumn": 12}, {"ruleId": "398", "severity": 1, "message": "402", "line": 235, "column": 37, "nodeType": "400", "messageId": "401", "endLine": 376, "endColumn": 12}, {"ruleId": "386", "severity": 1, "message": "403", "line": 237, "column": 28, "nodeType": "388", "messageId": "389", "endLine": 237, "endColumn": 33}, {"ruleId": "386", "severity": 1, "message": "404", "line": 237, "column": 42, "nodeType": "388", "messageId": "389", "endLine": 237, "endColumn": 48}, {"ruleId": "405", "severity": 1, "message": "406", "line": 282, "column": 30, "nodeType": "407", "messageId": "408", "endLine": 282, "endColumn": 32}, {"ruleId": "405", "severity": 1, "message": "406", "line": 283, "column": 30, "nodeType": "407", "messageId": "408", "endLine": 283, "endColumn": 32}, {"ruleId": "405", "severity": 1, "message": "406", "line": 284, "column": 30, "nodeType": "407", "messageId": "408", "endLine": 284, "endColumn": 32}, {"ruleId": "405", "severity": 1, "message": "406", "line": 285, "column": 30, "nodeType": "407", "messageId": "408", "endLine": 285, "endColumn": 32}, {"ruleId": "409", "severity": 1, "message": "410", "line": 578, "column": 6, "nodeType": "411", "endLine": 578, "endColumn": 28, "suggestions": "412"}, {"ruleId": "386", "severity": 1, "message": "413", "line": 729, "column": 13, "nodeType": "388", "messageId": "389", "endLine": 729, "endColumn": 21}, {"ruleId": "386", "severity": 1, "message": "414", "line": 33, "column": 8, "nodeType": "388", "messageId": "389", "endLine": 33, "endColumn": 21}, {"ruleId": "409", "severity": 1, "message": "415", "line": 138, "column": 6, "nodeType": "411", "endLine": 138, "endColumn": 58, "suggestions": "416"}, {"ruleId": "386", "severity": 1, "message": "417", "line": 29, "column": 3, "nodeType": "388", "messageId": "389", "endLine": 29, "endColumn": 15}, {"ruleId": "386", "severity": 1, "message": "418", "line": 58, "column": 26, "nodeType": "388", "messageId": "389", "endLine": 58, "endColumn": 43}, {"ruleId": "386", "severity": 1, "message": "419", "line": 47, "column": 9, "nodeType": "388", "messageId": "389", "endLine": 47, "endColumn": 26}, {"ruleId": "409", "severity": 1, "message": "420", "line": 132, "column": 6, "nodeType": "411", "endLine": 132, "endColumn": 38, "suggestions": "421"}, {"ruleId": "386", "severity": 1, "message": "418", "line": 48, "column": 26, "nodeType": "388", "messageId": "389", "endLine": 48, "endColumn": 43}, {"ruleId": "386", "severity": 1, "message": "422", "line": 64, "column": 9, "nodeType": "388", "messageId": "389", "endLine": 64, "endColumn": 25}, {"ruleId": "423", "severity": 1, "message": "424", "line": 614, "column": 21, "nodeType": "425", "messageId": "408", "endLine": 614, "endColumn": 36}, {"ruleId": "423", "severity": 1, "message": "426", "line": 615, "column": 21, "nodeType": "425", "messageId": "408", "endLine": 615, "endColumn": 32}, {"ruleId": "386", "severity": 1, "message": "427", "line": 7, "column": 10, "nodeType": "388", "messageId": "389", "endLine": 7, "endColumn": 20}, {"ruleId": "386", "severity": 1, "message": "428", "line": 6, "column": 10, "nodeType": "388", "messageId": "389", "endLine": 6, "endColumn": 18}, {"ruleId": "405", "severity": 1, "message": "406", "line": 106, "column": 77, "nodeType": "407", "messageId": "408", "endLine": 106, "endColumn": 79}, {"ruleId": "405", "severity": 1, "message": "406", "line": 110, "column": 73, "nodeType": "407", "messageId": "408", "endLine": 110, "endColumn": 75}, {"ruleId": "386", "severity": 1, "message": "429", "line": 63, "column": 11, "nodeType": "388", "messageId": "389", "endLine": 63, "endColumn": 24}, {"ruleId": "409", "severity": 1, "message": "430", "line": 77, "column": 8, "nodeType": "411", "endLine": 77, "endColumn": 45, "suggestions": "431"}, {"ruleId": "386", "severity": 1, "message": "432", "line": 39, "column": 10, "nodeType": "388", "messageId": "389", "endLine": 39, "endColumn": 25}, {"ruleId": "386", "severity": 1, "message": "433", "line": 51, "column": 10, "nodeType": "388", "messageId": "389", "endLine": 51, "endColumn": 26}, {"ruleId": "386", "severity": 1, "message": "434", "line": 51, "column": 28, "nodeType": "388", "messageId": "389", "endLine": 51, "endColumn": 47}, {"ruleId": "386", "severity": 1, "message": "435", "line": 727, "column": 15, "nodeType": "388", "messageId": "389", "endLine": 727, "endColumn": 25}, {"ruleId": "409", "severity": 1, "message": "436", "line": 1142, "column": 6, "nodeType": "411", "endLine": 1142, "endColumn": 27, "suggestions": "437"}, {"ruleId": "409", "severity": 1, "message": "438", "line": 92, "column": 6, "nodeType": "411", "endLine": 92, "endColumn": 35, "suggestions": "439"}, {"ruleId": "423", "severity": 1, "message": "440", "line": 516, "column": 3, "nodeType": "425", "messageId": "408", "endLine": 516, "endColumn": 13}, {"ruleId": "386", "severity": 1, "message": "441", "line": 983, "column": 9, "nodeType": "388", "messageId": "389", "endLine": 983, "endColumn": 25}, {"ruleId": "386", "severity": 1, "message": "441", "line": 69, "column": 9, "nodeType": "388", "messageId": "389", "endLine": 69, "endColumn": 25}, {"ruleId": "409", "severity": 1, "message": "438", "line": 108, "column": 6, "nodeType": "411", "endLine": 108, "endColumn": 35, "suggestions": "442"}, {"ruleId": "405", "severity": 1, "message": "406", "line": 394, "column": 86, "nodeType": "407", "messageId": "408", "endLine": 394, "endColumn": 88}, {"ruleId": "405", "severity": 1, "message": "406", "line": 396, "column": 79, "nodeType": "407", "messageId": "408", "endLine": 396, "endColumn": 81}, {"ruleId": "386", "severity": 1, "message": "441", "line": 77, "column": 9, "nodeType": "388", "messageId": "389", "endLine": 77, "endColumn": 25}, {"ruleId": "409", "severity": 1, "message": "438", "line": 142, "column": 6, "nodeType": "411", "endLine": 142, "endColumn": 23, "suggestions": "443"}, {"ruleId": "409", "severity": 1, "message": "438", "line": 77, "column": 6, "nodeType": "411", "endLine": 77, "endColumn": 21, "suggestions": "444"}, {"ruleId": "386", "severity": 1, "message": "445", "line": 103, "column": 15, "nodeType": "388", "messageId": "389", "endLine": 103, "endColumn": 30}, {"ruleId": "386", "severity": 1, "message": "445", "line": 121, "column": 15, "nodeType": "388", "messageId": "389", "endLine": 121, "endColumn": 30}, {"ruleId": "386", "severity": 1, "message": "446", "line": 121, "column": 13, "nodeType": "388", "messageId": "389", "endLine": 121, "endColumn": 32}, {"ruleId": "386", "severity": 1, "message": "447", "line": 11, "column": 3, "nodeType": "388", "messageId": "389", "endLine": 11, "endColumn": 19}, {"ruleId": "386", "severity": 1, "message": "448", "line": 13, "column": 8, "nodeType": "388", "messageId": "389", "endLine": 13, "endColumn": 16}, {"ruleId": "386", "severity": 1, "message": "449", "line": 14, "column": 8, "nodeType": "388", "messageId": "389", "endLine": 14, "endColumn": 18}, {"ruleId": "386", "severity": 1, "message": "450", "line": 15, "column": 8, "nodeType": "388", "messageId": "389", "endLine": 15, "endColumn": 23}, {"ruleId": "386", "severity": 1, "message": "451", "line": 80, "column": 13, "nodeType": "388", "messageId": "389", "endLine": 80, "endColumn": 20}, {"ruleId": "386", "severity": 1, "message": "452", "line": 81, "column": 13, "nodeType": "388", "messageId": "389", "endLine": 81, "endColumn": 17}, {"ruleId": "386", "severity": 1, "message": "453", "line": 82, "column": 13, "nodeType": "388", "messageId": "389", "endLine": 82, "endColumn": 25}, {"ruleId": "386", "severity": 1, "message": "454", "line": 10, "column": 3, "nodeType": "388", "messageId": "389", "endLine": 10, "endColumn": 7}, {"ruleId": "386", "severity": 1, "message": "455", "line": 18, "column": 3, "nodeType": "388", "messageId": "389", "endLine": 18, "endColumn": 10}, {"ruleId": "386", "severity": 1, "message": "456", "line": 24, "column": 11, "nodeType": "388", "messageId": "389", "endLine": 24, "endColumn": 19}, {"ruleId": "386", "severity": 1, "message": "457", "line": 25, "column": 13, "nodeType": "388", "messageId": "389", "endLine": 25, "endColumn": 23}, {"ruleId": "386", "severity": 1, "message": "458", "line": 46, "column": 24, "nodeType": "388", "messageId": "389", "endLine": 46, "endColumn": 39}, {"ruleId": "386", "severity": 1, "message": "459", "line": 7, "column": 3, "nodeType": "388", "messageId": "389", "endLine": 7, "endColumn": 8}, {"ruleId": "386", "severity": 1, "message": "460", "line": 8, "column": 3, "nodeType": "388", "messageId": "389", "endLine": 8, "endColumn": 10}, {"ruleId": "386", "severity": 1, "message": "461", "line": 10, "column": 3, "nodeType": "388", "messageId": "389", "endLine": 10, "endColumn": 7}, {"ruleId": "386", "severity": 1, "message": "462", "line": 11, "column": 3, "nodeType": "388", "messageId": "389", "endLine": 11, "endColumn": 13}, {"ruleId": "386", "severity": 1, "message": "463", "line": 26, "column": 19, "nodeType": "388", "messageId": "389", "endLine": 26, "endColumn": 31}, {"ruleId": "386", "severity": 1, "message": "456", "line": 27, "column": 19, "nodeType": "388", "messageId": "389", "endLine": 27, "endColumn": 27}, {"ruleId": "386", "severity": 1, "message": "464", "line": 28, "column": 26, "nodeType": "388", "messageId": "389", "endLine": 28, "endColumn": 35}, {"ruleId": "386", "severity": 1, "message": "465", "line": 29, "column": 19, "nodeType": "388", "messageId": "389", "endLine": 29, "endColumn": 27}, {"ruleId": "386", "severity": 1, "message": "466", "line": 40, "column": 9, "nodeType": "388", "messageId": "389", "endLine": 40, "endColumn": 14}, {"ruleId": "386", "severity": 1, "message": "466", "line": 94, "column": 9, "nodeType": "388", "messageId": "389", "endLine": 94, "endColumn": 14}, {"ruleId": "467", "severity": 1, "message": "468", "line": 303, "column": 34, "nodeType": "469", "messageId": "470", "endLine": 303, "endColumn": 35, "suggestions": "471"}, "no-unused-vars", "'useNavigate' is defined but never used.", "Identifier", "unusedVar", "'rememberMe' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "'Link' is defined but never used.", "'getCookie' is defined but never used.", "'setCookie' is defined but never used.", "'setSelectedFile' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'sectionHeaderX'.", "ArrowFunctionExpression", "unsafeRefs", "Function declared in a loop contains unsafe references to variable(s) 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'sectionHeaderX', 'isReportSummaryHeading', 'hasInsertedLineBreak', 'styledHTML', 'hasInsertedLineBreak', 'styledHTML', 'currentSection', 'currentSection', 'isReportSummaryHeading', 'isReportSummaryHeading', 'styledHTML'.", "'skewX' is assigned a value but never used.", "'scaleY' is assigned a value but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "react-hooks/exhaustive-deps", "React Hook React.useEffect has a missing dependency: 'report?.request_type'. Either include it or remove the dependency array.", "ArrayExpression", ["472"], "'response' is assigned a value but never used.", "'FilterAltIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchCompanies'. Either include it or remove the dependency array.", ["473"], "'revokeAccess' is defined but never used.", "'setIsCreatedModal' is assigned a value but never used.", "'requestTypeLabels' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'companyReports'. Either include it or remove the dependency array.", ["474"], "'handleRemoveUser' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'backgroundColor'.", "ObjectExpression", "Duplicate key 'borderColor'.", "'dialogOpen' is assigned a value but never used.", "'GrRevert' is defined but never used.", "'handleAddUser' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array. If 'fetchUsers' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["475"], "'initialSettings' is assigned a value but never used.", "'selectedTemplate' is assigned a value but never used.", "'setSelectedTemplate' is assigned a value but never used.", "'metricGrid' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchContentSettings', 'initializeDataWithConnectionCheck', and 'initializeSettings'. Either include them or remove the dependency array.", ["476"], "React Hook useEffect has missing dependencies: 'initializeCharts' and 'isDataLoaded'. Either include them or remove the dependency array.", ["477"], "Duplicate key 'dataLabels'.", "'hasAnyUsableData' is assigned a value but never used.", ["478"], ["479"], ["480"], "'variancePercent' is assigned a value but never used.", "'isNegativePriorYear' is assigned a value but never used.", "'CircularProgress' is defined but never used.", "'SyncIcon' is defined but never used.", "'UploadIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'minutes' is assigned a value but never used.", "'ampm' is assigned a value but never used.", "'displayHours' is assigned a value but never used.", "'Fade' is defined but never used.", "'Tooltip' is defined but never used.", "'InfoIcon' is defined but never used.", "'LaunchIcon' is defined but never used.", "'setShowBenefits' is assigned a value but never used.", "'Stack' is defined but never used.", "'Divider' is defined but never used.", "'Chip' is defined but never used.", "'IconButton' is defined but never used.", "'SettingsIcon' is defined but never used.", "'CheckIcon' is defined but never used.", "'SaveIcon' is defined but never used.", "'theme' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\’.", "Literal", "unnecessaryEscape", ["481", "482"], {"desc": "483", "fix": "484"}, {"desc": "485", "fix": "486"}, {"desc": "487", "fix": "488"}, {"desc": "489", "fix": "490"}, {"desc": "491", "fix": "492"}, {"desc": "493", "fix": "494"}, {"desc": "495", "fix": "496"}, {"desc": "497", "fix": "498"}, {"desc": "499", "fix": "500"}, {"messageId": "501", "fix": "502", "desc": "503"}, {"messageId": "504", "fix": "505", "desc": "506"}, "Update the dependencies array to be: [pdfUrl, report?.request_type, report.text]", {"range": "507", "text": "508"}, "Update the dependencies array to be: [page, rowsPerPage, refresh, searchTerm, sortConfig, fetchCompanies]", {"range": "509", "text": "510"}, "Update the dependencies array to be: [companyReports, currentCompanyId, reportDetail]", {"range": "511", "text": "512"}, "Update the dependencies array to be: [page, itemsPerPage, debouncedSearch, fetchUsers]", {"range": "513", "text": "514"}, "Update the dependencies array to be: [companyId, fetchContentSettings, initializeDataWithConnectionCheck, initializeSettings, reportId]", {"range": "515", "text": "516"}, "Update the dependencies array to be: [reportData, contentSettings, isDataLoaded, initializeCharts]", {"range": "517", "text": "518"}, "Update the dependencies array to be: [fiscalData, contentSettings, isDataLoaded, initializeCharts]", {"range": "519", "text": "520"}, "Update the dependencies array to be: [initializeCharts, isDataLoaded, operationalData]", {"range": "521", "text": "522"}, "Update the dependencies array to be: [initializeCharts, isDataLoaded, liquidityData]", {"range": "523", "text": "524"}, "removeEscape", {"range": "525", "text": "526"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "527", "text": "528"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", [19774, 19796], "[pdfUrl, report?.request_type, report.text]", [4476, 4528], "[page, rowsPerPage, refresh, searchTerm, sortConfig, fetchCompanies]", [3905, 3937], "[companyReports, currentCompanyId, reportDetail]", [2176, 2213], "[page, itemsPerPage, debouncedSearch, fetchUsers]", [44396, 44417], "[companyId, fetchContentSettings, initializeDataWithConnectionCheck, initializeSettings, reportId]", [3521, 3550], "[reportData, contentSettings, isDataLoaded, initializeCharts]", [3941, 3970], "[fiscalData, contentSettings, isDataLoaded, initializeCharts]", [6707, 6724], "[initializeCharts, isDataLoaded, operationalData]", [2674, 2689], "[initializeCharts, isDataLoaded, liquidityData]", [8098, 8099], "", [8098, 8098], "\\"]