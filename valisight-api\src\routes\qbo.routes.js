import { Router } from 'express';
import { qboController } from '../controllers/qbo.controller.js';
import { authenticate } from '../middleware/auth.middleware.js';

const qboRoute = Router();

qboRoute.get('/connect', authenticate, qboController.qboConnect);
qboRoute.get('/callback', qboController.qboCallback);
qboRoute.get('/status', authenticate, qboController.qboStatus);
qboRoute.post('/disconnect', authenticate, qboController.qboDisconnect);

qboRoute.post('/sync/:reportType', authenticate, qboController.syncHandler);

export default qboRoute;
