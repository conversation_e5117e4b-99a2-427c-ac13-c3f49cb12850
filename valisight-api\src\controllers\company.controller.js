import * as companyService from '../services/company.service.js';
import { companyValidation } from '../validations/company.validation.js';
import { <PERSON>rror<PERSON>and<PERSON> } from '../utils/errorHandler.js';

export const registerCompany = async (req, res) => {
  try {
    const { name, fiscal_year_end, description, naics, users } = req.body;
    // add validation
    let { error } = companyValidation.registerCompany.validate({
      name,
      fiscal_year_end,
      description,
      naics,
      users,
    });
    if (error) {
      throw new ErrorHandler(error.details[0].message, 400);
    }

    const result = await companyService.registerCompany(
      req.body,
      req.user.id,
      users,
    );
    res.json(result);
  } catch (error) {
    if (error instanceof ErrorHandler) {
      res.status(error.statusCode).json({
        success: false,
        statusCode: error.statusCode,
        message: error.message,
      });
    } else {
      res.status(500).json({
        success: false,
        statusCode: 500,
        message: 'Internal server error',
      });
    }
  }
};

export const getCompanyLocation = async (req, res) => {
  try {
    const {} = req.body;
    // add validation

    res.json(
      await companyService.getCompanyLocation(username, password, email),
    );
  } catch (error) {
    console.log(error.message);
    const { status } = error;
    const s = status ? status : 500;
    res.status(s).json({
      success: error.success,
      statusCode: error.statusCode,
      message: error.message,
    });
  }
};

export const getAllCompanies = async (req, res) => {
  try {
    // get user id from token
    const { page, pageSize, search, sortBy, sortOrder } = req.query;
    let { error } = companyValidation.getAllCompanies.validate({
      page,
      pageSize,
      search,
    });
    if (error) {
      throw new ErrorHandler(error.details[0].message, 400);
    }

    res.json(
      await companyService.getAllCompanies(
        req.user.id,
        parseInt(page),
        parseInt(pageSize),
        search,
        req.user.isAdmin,
        sortBy,
        sortOrder,
      ),
    );
  } catch (error) {
    console.error(error);
    throw new ErrorHandler('Error while fetching company details', 500);
  }
};


export const getCompanyDetail = async (req, res, next) => {
  try {
    // get user id from token and pass
    const { id, isAdmin } = req.user;

    const companyId = req.params.id;

    let { error } = companyValidation.getOneCompany.validate({ companyId });
    if (error) {
      throw new ErrorHandler(error.details[0].message, 400);
    }
   const result = await companyService.getCompanyDetail(id, parseInt(companyId), isAdmin);

    res.status(result.statusCode).json(result);
  } catch (error) {
     next(error);
  }
};

export const updateCompanyDetail = async (req, res) => {
  try {
    // Extracted the user ID from user token (eg. token middleware)
    const { id: userId } = req.user;

    //Extract the company ID from request parameters
    const companyId = parseInt(req.params.id);

    const { addedUsers: activeSharedUsers, ...updateData } = req.body;

    // validate the inputs
    const { error } = companyValidation.updateCompanyDetail.validate({
      userId,
      companyId,
    });

    // if validation fail throw error
    if (error) {
      throw new ErrorHandler(error.details[0].message, 400);
    }

    res.json(
      await companyService.updateCompanyDetail(
        userId,
        parseInt(companyId),
        updateData,
        activeSharedUsers,
      ),
    );
  } catch (error) {
    console.error(error);
    throw new ErrorHandler('An error occurred while trying to update the company', 500);
  }
};

export const deleteCompany = async (req, res) => {
  try {
    // get user id from token and pass
    const { id: userId, isAdmin, email } = req.user;
    const companyId = parseInt(req.params.id);

    const { error } = companyValidation.deleteCompany.validate({
      userId,
      companyId,
    });

    if (error) {
      throw new ErrorHandler(error.details[0].message, 400);
    }

    res.json(
      await companyService.deleteCompany(userId, companyId, isAdmin, email),
    );
  } catch (error) {
    console.error('Error deleting company:', error);
    throw new ErrorHandler('An error occurred while deleting the company.', 500);
  }
};

export const uploadCompanyInfoFiles = async (req, res, next) => {
  try {
    const { id: userId } = req.user;
    const companyId = parseInt(req.params.id);
    res.json(
      await companyService.uploadCompanyInfoFiles(req.body, companyId, userId),
    );
  } catch (error) {
    console.error(error);
    next(error);
  }
};
