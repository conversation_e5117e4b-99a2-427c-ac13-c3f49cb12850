import CircleIcon from "@mui/icons-material/Circle";
import MoreHorizIcon from "@mui/icons-material/MoreHoriz";
import {
  Avatar,
  Box,
  Button,
  Card,
  Chip,
  CircularProgress,
  Grid2 as Grid,
  Skeleton,
  Stack,
  Typography,
} from "@mui/material";
import React, { useCallback, useEffect, useState } from "react";
import { Link, useLocation, useParams } from "react-router-dom";
import Swal from "sweetalert2";
import { getOneById } from "../../../services/company";
import {
  connectQBO,
  disconnectQBO,
  getQBOStatus,
  syncAccounts,
  syncAPAging,
  syncARAging,
  syncBalanceSheet,
  syncProfitLoss,
  syncTrialBalance,
} from "../../../services/qbo";
import Breadcrumb from "../../../shared-components/BreadCrumbs";
import { getCookie, setCookie } from "../../../utils/cookies";
import { formatDate, getAvatarInitials } from "../../../utils/shared";
import qboButton from "./../../../assets/C2QB_green_btn_med_default.svg";
import qboButtonHover from "./../../../assets/C2QB_green_btn_med_hover.svg";
import CSVUploadModal from "./CSVModal";
import "./Detail.css";
import EditCompany from "./Edit";
import Reports from "./Reports";
import UploadableSection from "./UploadableSection";
import QuickBooksConnection from "./QuickBooksConnection";
import ReportSettings from "./ReportSettings";

const SYNC_TYPES = {
  CHART_OF_ACCOUNTS: "CHART_OF_ACCOUNTS",
  TRIAL_BALANCE: "TRIAL_BALANCE",
  PROFIT_AND_LOSS: "PROFIT_AND_LOSS",
  BALANCE_SHEET: "BALANCE_SHEET",
  AP_AGING: "AP_AGING",
  AR_AGING: "AR_AGING",
};

function CompanyDetail() {
  const location = useLocation();
  const params = useParams();
  const id = location?.state?.id || params?.id;
  const [activeTab, setActiveTab] = useState(
    location?.state?.activeTab || "companyInfo"
  );
  const [selectedFile, setSelectedFile] = useState(null);
  const [company, setCompany] = useState(null);
  const [fileTypeSection, setFileTypeSection] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [qboConnected, setQboConnected] = useState(false);
  const [qboLoading, setQboLoading] = useState(false);
  const [qboStatus, setQboStatus] = useState(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const [syncLoading, setSyncLoading] = useState({
    [SYNC_TYPES.CHART_OF_ACCOUNTS]: false,
    [SYNC_TYPES.TRIAL_BALANCE]: false,
    [SYNC_TYPES.PROFIT_AND_LOSS]: false,
    [SYNC_TYPES.BALANCE_SHEET]: false,
    [SYNC_TYPES.AP_AGING]: false,
    [SYNC_TYPES.AR_AGING]: false,
  });

  const handleOpenModal = (type) => {
    setShowModal(true);
    setFileTypeSection(type);
  };

  const handleUpload = () => {
    if (selectedFile) {
    }
  };

  const handleSync = async (type) => {
    setSyncLoading((prev) => ({ ...prev, [type]: true }));

    try {
      const payload = {
        userId: company?.userId,
        companyId: company?.id,
        dumpToDatabase: true,
      };

      const syncMap = {
        [SYNC_TYPES.CHART_OF_ACCOUNTS]: {
          fn: syncAccounts,
          message: "Chart of Accounts synced successfully",
        },
        [SYNC_TYPES.TRIAL_BALANCE]: {
          fn: syncTrialBalance,
          message: "Trial Balance synced successfully",
        },
        [SYNC_TYPES.PROFIT_AND_LOSS]: {
          fn: syncProfitLoss,
          message: "Profit and Loss synced successfully",
        },
        [SYNC_TYPES.BALANCE_SHEET]: {
          fn: syncBalanceSheet,
          message: "Balance Sheet synced successfully",
        },
        [SYNC_TYPES.AP_AGING]: {
          fn: syncAPAging,
          message: "AP Aging synced successfully",
        },
        [SYNC_TYPES.AR_AGING]: {
          fn: syncARAging,
          message: "AR Aging synced successfully",
        },
      };

      const syncItem = syncMap[type];
      if (!syncItem) throw new Error(`Unknown sync type: ${type}`);

      const response = await syncItem.fn(payload);

      if (response.data?.success) {
        Swal.fire({
          icon: "success",
          title: "Sync Successful",
          text: syncItem.message,
          confirmButtonColor: "#033BD7",
        });
        await fetchData();
      } else {
        throw new Error(response.data?.message || "Sync failed");
      }
    } catch (error) {
      Swal.fire({
        icon: "error",
        title: "Sync Error",
        text: error.response.data?.message || "An error occurred during sync.",
        confirmButtonColor: "#D33",
      });
    } finally {
      setSyncLoading((prev) => ({ ...prev, [type]: false }));
    }
  };

  const fetchData = useCallback(async () => {
    try {
      const response = await getOneById(id);

      if (response.data.success) {
        setCompany(response.data.company);
        setQboConnected(
          response.data.company.qboConnectionStatus === "CONNECTED"
        );
        setQboStatus(response.data.company);
      }
    } catch (error) {
      console.error("Error fetching company details:", error);
      Swal.fire({
        icon: "error",
        title: "Failed to Fetch Company",
        text:
          error.response?.data?.message ||
          "Failed to fetch Company Details. Please try again.",
        confirmButtonColor: "#033BD7",
      });
    } finally {
    }
  }, [id]);

  const fetchQBOStatus = useCallback(async () => {
    try {
      setQboLoading(true);
      const response = await getQBOStatus(id);
      if (response.data) {
        setQboConnected(response.data.qboConnectionStatus === "CONNECTED");
        setQboStatus(response.data);
      }
    } catch (error) {
      console.error("Error fetching QBO status:", error);
      // Swal.fire({
      //   icon: "error",
      //   title: "Connection Failed",
      //   text:
      //     error.response?.data?.message ||
      //     "Failed to fetch QBO status. Please try again.",
      //   confirmButtonColor: "#033BD7",
      // });
      setQboConnected(false);
    } finally {
      setQboLoading(false);
    }
  }, [id]);

  const handleQBOConnect = async () => {
    try {
      setQboLoading(true);

      const response = await connectQBO(id);

      if (response.data.success && response.data?.data?.url) {
        window.location.href = response.data?.data?.url;
      } else {
        throw new Error("Failed to get authorization URL");
      }
    } catch (error) {
      console.error("Error connecting to QBO:", error);
      setQboLoading(false);
    }
  };

  const handleQBODisconnect = async () => {
    try {
      const result = await Swal.fire({
        title: "Disconnect Fintuition from QuickBooks?",
        text: "This will stop syncing your financial data. You can reconnect anytime.",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#ef4444", //Rd for destructive action
        cancelButtonColor: "transparent", // Remove fill color
        confirmButtonText: "DISCONNECT",
        cancelButtonText: "CANCEL",
        width: 500,
        customClass: {
          cancelButton: 'custom-cancel-button'
        },
        didOpen: () => {
          const style = document.createElement('style');
          style.textContent = `
      .custom-cancel-button {
        border: 2px solid #1c66bd !important;
        background-color: transparent !important;
        color: #1c66bd !important;
      }
      .custom-cancel-button:hover {
        background-color: #1c66bd !important;
        color: white !important;
      }
    `;
          document.head.appendChild(style);
        }
      });


      if (result.isConfirmed) {
        setQboLoading(true);
        await disconnectQBO(id);
        await fetchQBOStatus();

        Swal.fire({
          icon: "success",
          title: "Successfully Disconnected",
          text: "Fintuition has been disconnected from QuickBooks. You can reconnect anytime.",
          confirmButtonColor: "#033BD7",
          confirmButtonText: "Got it",
        });
      }
    } catch (error) {
      console.error("Error disconnecting QBO:", error);

      Swal.fire({
        icon: "error",
        title: "Disconnect Failed",
        text:
          error.response?.data?.message ||
          "Unable to disconnect from QuickBooks. Please check your connection and try again.",
        confirmButtonColor: "#033BD7",
        confirmButtonText: "Try again",
        showCancelButton: true,
        cancelButtonText: "Cancel",
        cancelButtonColor: "#64748b",
      });
    } finally {
      setQboLoading(false);
    }
  };

  // const checkQBOMessages = () => {
  //   const qboStatus = getCookie("qbo_status");
  //   if (qboStatus) {
  //     const decodedMessage = decodeURIComponent(qboStatus);
  //     Swal.fire({
  //       icon: "success",
  //       title: "QuickBooks Connected",
  //       text: decodedMessage,
  //       confirmButtonColor: "#033BD7",
  //     });

  //     setCookie("qbo_status", "", -1);
  //   }

  //   // const qboError = getCookie("qbo_error");
  //   // if (qboError) {
  //   //   const decodedMessage = decodeURIComponent(qboError);
  //   //   Swal.fire({
  //   //     icon: "error",
  //   //     title: "QuickBooks Connection Failed",
  //   //     text: decodedMessage,
  //   //     confirmButtonColor: "#033BD7",
  //   //   });

  //   //   setCookie("qbo_error", "", -1);
  //   // }
  // };

  useEffect(() => {
    if (location?.state?.activeTab) {
      setActiveTab(location.state.activeTab);
    }
  }, [location?.state?.activeTab]);

  useEffect(() => {
    if (!id) return;

    const fetchAll = async () => {
      setInitialLoading(true);
      await fetchData();
      setInitialLoading(false);
    };

    fetchAll();

    // Check if user returned from QuickBooks connect page without connecting
    // (e.g., look for a specific cookie or status in the URL)
    // const qboStatus = getCookie("qbo_status");
    // const qboError = getCookie("qbo_error");
    // if (qboStatus) {
    //   const decodedMessage = decodeURIComponent(qboStatus);
    //   Swal.fire({
    //     icon: "success",
    //     title: "QuickBooks Connected",
    //     text: decodedMessage,
    //     confirmButtonColor: "#033BD7",
    //   });
    //   setCookie("qbo_status", "", -1);
    // } else if (qboError) {
    //   const decodedMessage = decodeURIComponent(qboError);
    //   Swal.fire({
    //     icon: "error",
    //     title: "QuickBooks Connection Failed",
    //     text: decodedMessage,
    //     confirmButtonColor: "#033BD7",
    //   });
    //   setCookie("qbo_error", "", -1);
    // }
  }, [id, fetchData, location.pathname]);

  // useEffect(() => {
  //   checkQBOMessages();
  // }, [location.pathname]);

  const handleEdit = () => {
    if (qboLoading || initialLoading) return;

    if (company) {
      setIsEditModalOpen(true);
    }
  };

  const handleSaveCompany = (updatedCompany) => {
    setCompany(updatedCompany);
    fetchData();
  };
  const CompanyHeaderSkeleton = () => (
    <Card
      sx={{
        p: 3,
        mb: 2,
        border: "1px solid #e5e7eb",
        borderRadius: 2,
        boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
        backgroundColor: "#fff",
      }}
    >
      {/* Header Section - matches Stack direction="row" justifyContent="space-between" */}
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="flex-start"
        sx={{ mb: 3 }}
      >
        {/* Left side - Avatar and company info */}
        <Stack direction="row" spacing={3} alignItems="center">
          {/* Avatar - matches 64x64 size */}
          <Skeleton
            variant="rounded"
            width={64}
            height={64}
            sx={{ borderRadius: 2 }}
          />

          <Box>
            {/* Company name - matches Typography variant="h5" fontSize="1.5rem" */}
            <Skeleton
              variant="text"
              width={200}
              height={32}
              sx={{ mb: 0.5, fontSize: "1.5rem" }}
            />

            {/* QuickBooks Company line - matches Typography variant="body1" fontSize="0.95rem" */}
            <Skeleton
              variant="text"
              width={280}
              height={22}
              sx={{ mb: 1, fontSize: "0.95rem" }}
            />

            {/* Connection Status line - matches Stack with chip */}
            <Stack direction="row" spacing={2} alignItems="center">
              <Skeleton
                variant="rounded"
                width={90}
                height={22}
                sx={{ borderRadius: 3 }}
              />
            </Stack>
          </Box>
        </Stack>

        {/* Right side - Action buttons */}
        <Stack direction="row" spacing={2} alignItems="center">
          {/* More options button - matches 60x36 */}
          <Skeleton variant="rounded" width={60} height={36} />

          {/* QBO connect/disconnect button - matches 220x36 */}
          <Skeleton variant="rounded" width={220} height={36} />

          {/* Edit button - matches 180x36 */}
          <Skeleton variant="rounded" width={180} height={36} />
        </Stack>
      </Stack>

      {/* Company Details Grid - matches pt: 2, borderTop */}
      <Box sx={{ pt: 2, borderTop: 1, borderColor: "divider" }}>
        <Grid container spacing={2}>
          {/* Location - matches Grid size={{ xs: 6, lg: 3 }} */}
          <Grid size={{ xs: 6, lg: 3 }}>
            <Skeleton
              variant="text"
              width={60}
              height={14}
              sx={{ mb: 0.5, fontSize: "0.75rem" }}
            />
            <Skeleton
              variant="text"
              width="100%"
              height={18}
              sx={{ fontSize: "0.875rem" }}
            />
          </Grid>

          {/* Fiscal Year End */}
          <Grid size={{ xs: 6, lg: 3 }}>
            <Skeleton
              variant="text"
              width={90}
              height={14}
              sx={{ mb: 0.5, fontSize: "0.75rem" }}
            />
            <Skeleton
              variant="text"
              width="100%"
              height={18}
              sx={{ fontSize: "0.875rem" }}
            />
          </Grid>

          {/* NAICS */}
          <Grid size={{ xs: 6, lg: 3 }}>
            <Skeleton
              variant="text"
              width={40}
              height={14}
              sx={{ mb: 0.5, fontSize: "0.75rem" }}
            />
            <Skeleton
              variant="text"
              width="100%"
              height={18}
              sx={{ fontSize: "0.875rem" }}
            />
          </Grid>

          {/* Market */}
          <Grid size={{ xs: 6, lg: 3 }}>
            <Skeleton
              variant="text"
              width={50}
              height={14}
              sx={{ mb: 0.5, fontSize: "0.75rem" }}
            />
            <Skeleton
              variant="text"
              width="100%"
              height={18}
              sx={{ fontSize: "0.875rem" }}
            />
          </Grid>
        </Grid>
      </Box>

      {/* Critical Company Info - matches mt: 2, pt: 2, borderTop */}
      <Box sx={{ mt: 2, pt: 2, borderTop: 1, borderColor: "divider" }}>
        <Skeleton
          variant="text"
          width={130}
          height={14}
          sx={{ mb: 0.5, fontSize: "0.75rem" }}
        />
        <Skeleton
          variant="text"
          width="85%"
          height={18}
          sx={{ fontSize: "0.875rem" }}
        />
        <Skeleton
          variant="text"
          width="60%"
          height={18}
          sx={{ fontSize: "0.875rem", mt: 0.5 }}
        />
      </Box>
    </Card>
  );

  const TabsSkeleton = () => (
    <Box sx={{ px: 3, py: 2 }}>
      {/* Matches Stack direction="row" spacing={4} borderBottom */}
      <Stack
        direction="row"
        spacing={4}
        sx={{ borderBottom: 1, borderColor: "divider" }}
      >
        {/* Company info tab - matches fontSize: 14, fontWeight: 600, pb: 1 */}
        <Skeleton
          variant="text"
          width={100}
          height={20}
          sx={{
            pb: 1,
            fontSize: 14,
          }}
        />
        {/* Reports tab */}
        <Skeleton
          variant="text"
          width={70}
          height={20}
          sx={{
            pb: 1,
            fontSize: 14,
          }}
        />
        {/* Report Summary tab */}
        <Skeleton
          variant="text"
          width={110}
          height={20}
          sx={{
            pb: 1,
            fontSize: 14,
          }}
        />
      </Stack>
    </Box>
  );

  const UploadableSectionSkeleton = () => (
    <Grid container spacing={1.5}>
      {[...Array(6)].map((_, i) => (
        <Grid size={{ xs: 12, sm: 6, lg: 4 }} key={i}>
          <Card
            sx={{
              p: 1.5,
              border: "1px solid #e5e7eb",
              borderRadius: 1,
              boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
              backgroundColor: "#fff",
            }}
          >
            {/* Header with title and buttons */}
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="flex-start"
              sx={{ mb: 1 }}
            >
              {/* Section title */}
              <Skeleton
                variant="text"
                width={140}
                height={18}
                sx={{ fontSize: "0.875rem", fontWeight: 600 }}
              />

              {/* Buttons stack */}
              <Stack direction="row" spacing={0.5}>
                <Skeleton variant="rounded" width={50} height={20} />
              </Stack>
            </Stack>

            {/* Description lines */}
            
            <Skeleton
              variant="text"
              width="75%"
              height={14}
              sx={{ fontSize: "0.75rem" , marginBottom : '20px'}}
            />

            {/* Status or additional info */}
             <Stack direction="row" spacing={0.5}>
                <Skeleton variant="rounded" width={50} height={24} />
                <Skeleton variant="rounded" width={50} height={24} />
              </Stack>
            
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  return (
    <>
      {/* Header */}
      <Box sx={{ my: 2 }} />
      <Breadcrumb companyName={company?.name} />

      <Box className="px-4">
        {/* Main Content */}
        <Box sx={{ px: 2 }}>
          {/* Tabs */}
          <Box sx={{ px: 3, py: 2 }}>
            {initialLoading ? (
              <TabsSkeleton />
            ) : (
              <Stack
                direction="row"
                spacing={4}
                sx={{ borderBottom: 1, borderColor: "divider" }}
              >
                <Button
                  onClick={() => setActiveTab("companyInfo")}
                  sx={{
                    pb: 1,
                    fontSize: 14,
                    fontWeight: 600,
                    textTransform: "none",
                    color:
                      activeTab === "companyInfo"
                        ? "primary.main"
                        : "text.secondary",
                    borderBottom: 2,
                    borderColor:
                      activeTab === "companyInfo"
                        ? "primary.main"
                        : "transparent",
                    borderRadius: 0,
                    "&:hover": {
                      backgroundColor: "transparent",
                      color:
                        activeTab === "companyInfo"
                          ? "primary.main"
                          : "text.primary",
                    },
                  }}
                >
                  Company info
                </Button>
                <Button
                  onClick={() => setActiveTab("reports")}
                  sx={{
                    pb: 1,
                    fontSize: 14,
                    fontWeight: 600,
                    textTransform: "none",
                    color:
                      activeTab === "reports"
                        ? "primary.main"
                        : "text.secondary",
                    borderBottom: 2,
                    borderColor:
                      activeTab === "reports" ? "primary.main" : "transparent",
                    borderRadius: 0,
                    "&:hover": {
                      backgroundColor: "transparent",
                      color:
                        activeTab === "reports"
                          ? "primary.main"
                          : "text.primary",
                    },
                  }}
                >
                  Reports
                </Button>
                <Button
                  onClick={() => setActiveTab("report-settings")}
                  sx={{
                    pb: 1,
                    fontSize: 14,
                    fontWeight: 600,
                    textTransform: "none",
                    color:
                      activeTab === "report-settings"
                        ? "primary.main"
                        : "text.secondary",
                    borderBottom: 2,
                    borderColor:
                      activeTab === "report-settings" ? "primary.main" : "transparent",
                    borderRadius: 0,
                    "&:hover": {
                      backgroundColor: "transparent",
                      color:
                        activeTab === "report-settings"
                          ? "primary.main"
                          : "text.primary",
                    },
                  }}
                >
                  Report Settings
                </Button>
              </Stack>
            )}
          </Box>

          {/* Conditional Rendering of Content based on Active Tab */}
          <Box sx={{ p: 1.5 }}>
            {initialLoading ? (
              <Box>
                <CompanyHeaderSkeleton />
                <UploadableSectionSkeleton />
              </Box>
            ) : activeTab === "companyInfo" ? (
              <Box>
                {/* Company Info Card */}
                <Card
                  sx={{
                    p: 3,
                    mb: 2,
                    border: "1px solid #e5e7eb",
                    borderRadius: 1,
                    boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1)",
                    "&:hover": {
                      boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                    },
                    transition: "all 0.2s ease-in-out",
                    backgroundColor: "#fff",
                  }}
                >
                  {/* Header Section */}
                  <Stack
                    direction="row"
                    justifyContent="space-between"
                    alignItems="flex-start"
                    sx={{ mb: 3 }}
                  >
                    <Stack direction="row" spacing={3} alignItems="center">
                      {company?.logo ? (
                        <Avatar
                          src={company.logo}
                          alt="Company Logo"
                          sx={{
                            height: 64,
                            width: 64,
                            borderRadius: 2,
                          }}
                        />
                      ) : (
                        <Avatar
                          sx={{
                            width: 64,
                            height: 64,
                            background: "#3b82f6",
                            borderRadius: 2,
                            fontSize: 20,
                            fontWeight: "bold",
                            color: "#fff",
                          }}
                        >
                          {getAvatarInitials(company?.name)}
                        </Avatar>
                      )}

                      <Box>
                        <Typography
                          variant="h5"
                          sx={{
                            fontWeight: 600,
                            mb: 0.5,
                            fontSize: "1.5rem",
                            color: "#111827",
                          }}
                        >
                          {company?.name}
                        </Typography>

                        {/* QBO Company Name */}

                        <Typography
                          variant="body1"
                          sx={{
                            color: "#6b7280",
                            mb: 1,
                            fontSize: "0.95rem",
                          }}
                        >
                          {company?.qboCompanyName ? (
                            <>
                              QuickBooks Company:&nbsp;
                              <span
                                style={{
                                  color: "#1976d2",
                                  fontWeight: "500",
                                  textDecoration: "none",
                                }}
                              >
                                {company?.qboCompanyName}
                              </span>
                            </>
                          ) : null}
                        </Typography>

                        {/* Connection Status */}
                        <Stack direction="row" spacing={2} alignItems="center">
                          <Stack
                            direction="row"
                            spacing={1}
                            alignItems="center"
                          >
                            {qboConnected ? (
                              <Chip
                                icon={
                                  <CircleIcon
                                    sx={{
                                      fontSize: "8px !important",
                                      color: "#10b981 !important",
                                    }}
                                  />
                                }
                                label="Connected"
                                size="small"
                                sx={{
                                  backgroundColor: "#d1fae5",
                                  color: "#065f46",
                                  border: "none",
                                  height: 24,
                                  fontSize: 12,
                                  fontWeight: 500,
                                  borderRadius: "12px",
                                  "& .MuiChip-icon": {
                                    color: "#10b981",
                                    marginLeft: "6px",
                                  },
                                  "& .MuiChip-label": {
                                    paddingLeft: "9px",
                                    paddingRight: "8px",
                                  },
                                }}
                              />
                            ) : (
                              <Chip
                                icon={
                                  <CircleIcon
                                    sx={{
                                      fontSize: "8px !important",
                                      color: "#ef4444 !important",
                                    }}
                                  />
                                }
                                label="Not Connected"
                                size="small"
                                sx={{
                                  backgroundColor: "#fee2e2",
                                  color: "#991b1b",
                                  border: "none",
                                  height: 24,
                                  fontSize: 12,
                                  fontWeight: 500,
                                  borderRadius: "12px",
                                  "& .MuiChip-icon": {
                                    color: "#ef4444",
                                    marginLeft: "6px",
                                  },
                                  "& .MuiChip-label": {
                                    paddingLeft: "9px",
                                    paddingRight: "8px",
                                  },
                                }}
                              />
                            )}
                          </Stack>

                          {/* {qboStatus?.qboRealmID && (
    <Typography variant="body2" sx={{ color: "#6b7280", fontSize: "0.875rem" }}>
      (ID: {qboStatus.qboRealmID})
    </Typography>
  )} */}
                        </Stack>
                      </Box>
                    </Stack>

                    {/* Action Buttons */}
                    <Stack direction="row" spacing={2} alignItems="center">
                      <Button
                        variant="contained"
                        size="small"
                        sx={{
                          minWidth: 60,
                          height: 36,
                          padding: 0,
                          backgroundColor: "#fff",
                          color: "#374151",
                          border: "1px solid #e5e7eb",
                          boxShadow: "none",
                          "&:hover": {
                            backgroundColor: "#f3f4f6",
                            boxShadow: "none",
                          },
                        }}
                      >
                        <MoreHorizIcon />
                      </Button>

                      {qboConnected ? (
                        <Button
                          variant="contained"
                          size="small"
                          sx={{
                            fontSize: "14px",
                            fontWeight: 540,
                            px: 12,
                            minWidth: 250,
                            height: 36,
                            textTransform: "none",
                            borderRadius: "2",
                            backgroundColor: "#dc2626",
                            color: "#fff",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            "&:hover": {
                              backgroundColor: "#b91c1c",
                            },
                            "&:disabled": {
                              backgroundColor: "#e5e7eb",
                              color: "#9ca3af",
                            },
                          }}
                          onClick={handleQBODisconnect}
                          disabled={qboLoading || initialLoading}
                        >
                          {qboLoading
                            ? "Disconnecting..."
                            : "DISCONNECT FROM QUICKBOOKS"}
                        </Button>
                      ) : (
                        <Box
                          sx={{
                            position: "relative",
                            minWidth: 220,
                            height: 36,
                          }}
                        >
                          <Box
                            component="img"
                            src={qboButton}
                            alt="Connect to QuickBooks"
                            sx={{
                              cursor: "pointer",
                              height: 36,
                              width: "100%",
                              transition: "opacity 0.2s ease-in-out",
                              opacity: qboLoading || initialLoading ? 0.5 : 1,
                              pointerEvents:
                                qboLoading || initialLoading ? "none" : "auto",
                              "&:hover": { opacity: 0 },
                            }}
                            onClick={
                              !qboLoading && !initialLoading
                                ? handleQBOConnect
                                : undefined
                            }
                          />
                          <Box
                            component="img"
                            src={qboButtonHover}
                            alt="Connect to QuickBooks"
                            sx={{
                              position: "absolute",
                              top: 0,
                              left: 0,
                              height: 36,
                              width: "100%",
                              opacity: 0,
                              transition: "opacity 0.2s ease-in-out",
                              "&:hover": { opacity: 1 },
                              pointerEvents:
                                qboLoading || initialLoading ? "none" : "auto",
                            }}
                            onClick={
                              !qboLoading && !initialLoading
                                ? handleQBOConnect
                                : undefined
                            }
                          />
                          {(qboLoading || initialLoading) && (
                            <Box
                              sx={{
                                position: "absolute",
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                backgroundColor: "rgba(255, 255, 255, 0.8)",
                                borderRadius: 1,
                              }}
                            >
                              <CircularProgress size={18} />
                            </Box>
                          )}
                        </Box>
                      )}

                      <Button
                        variant="outlined"
                        size="small"
                        sx={{
                          fontSize: 14,
                          px: 3,
                          minWidth: 180,
                          height: 36,
                          textTransform: "none",
                          borderColor: "#d1d5db",
                          color: "#374151",
                          fontWeight: 500,
                          borderRadius: "2",
                          whiteSpace: "nowrap",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          "&:hover": {
                            backgroundColor: "#f9fafb",
                            borderColor: "#9ca3af",
                          },
                          "&:disabled": {
                            backgroundColor: "#f3f4f6",
                            color: "#9ca3af",
                            borderColor: "#e5e7eb",
                          },
                        }}
                        onClick={() => handleEdit()}
                        disabled={qboLoading || initialLoading}
                        fullWidth={true}
                      >
                        EDIT COMPANY INFO
                      </Button>
                    </Stack>
                  </Stack>

                  {/* Company Details Grid */}

                  <Box sx={{ pt: 2, borderTop: 1, borderColor: "divider" }}>
                    <Grid container spacing={2}>
                      {/* Location */}
                      {company?.country && (
                        <Grid size={{ xs: 6, lg: 3 }}>
                          <Typography
                            variant="caption"
                            sx={{
                              color: "text.secondary",
                              fontWeight: 500,
                              display: "block",
                              mb: 0.5,
                              textTransform: "none",
                            }}
                          >
                            Location
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{ fontWeight: 600, color: "text.primary" }}
                          >
                            {[company?.country, company?.state, company?.city]
                              .filter(Boolean)
                              .join(", ")}
                          </Typography>
                        </Grid>
                      )}

                      {/* Fiscal Year End */}
                      <Grid size={{ xs: 6, lg: 3 }}>
                        <Typography
                          variant="caption"
                          sx={{
                            color: "text.secondary",
                            fontWeight: 500,
                            display: "block",
                            mb: 0.5,
                            textTransform: "none",
                          }}
                        >
                          Fiscal year end
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ fontWeight: 600, color: "text.primary" }}
                        >
                          {formatDate(company?.fiscal_year_end)}
                        </Typography>
                      </Grid>

                      <Grid size={{ xs: 6, lg: 3 }}>
                        <Typography
                          variant="caption"
                          sx={{
                            color: "text.secondary",
                            fontWeight: 500,
                            display: "block",
                            mb: 0.5,
                            textTransform: "none",
                          }}
                        >
                          Naics
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ fontWeight: 600, color: "text.primary" }}
                        >
                          {company?.naics}
                        </Typography>
                      </Grid>

                      {/* Market */}
                      {company?.market && (
                        <Grid size={{ xs: 6, lg: 3 }}>
                          <Typography
                            variant="caption"
                            sx={{
                              color: "text.secondary",
                              fontWeight: 500,
                              display: "block",
                              mb: 0.5,
                              textTransform: "none",
                            }}
                          >
                            Market
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{ fontWeight: 600, color: "text.primary" }}
                          >
                            {company?.market}
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </Box>
                  {/* Critical Company Info */}
                  {company?.description && (
                    <Box
                      sx={{
                        mt: 2,
                        pt: 2,
                        borderTop: 1,
                        borderColor: "divider",
                      }}
                    >
                      <Typography
                        variant="caption"
                        sx={{
                          color: "text.secondary",
                          fontWeight: 500,
                          display: "block",
                          mb: 0.5,
                          textTransform: "none",
                        }}
                      >
                        Critical company info
                      </Typography>
                      {company.description.length > 60 ? (
                        <Box component="details" sx={{ cursor: "pointer" }}>
                          <Typography
                            component="summary"
                            variant="body2"
                            sx={{
                              fontWeight: 600,
                              color: "text.primary",
                              "&:hover": { color: "primary.main" },
                              transition: "color 0.2s ease-in-out",
                            }}
                          >
                            {company.description.slice(0, 50)}...
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{ mt: 1, color: "text.secondary" }}
                          >
                            {company.description}
                          </Typography>
                        </Box>
                      ) : (
                        <Typography
                          variant="body2"
                          sx={{ fontWeight: 600, color: "text.primary" }}
                        >
                          {company?.description}
                        </Typography>
                      )}
                    </Box>
                  )}
                </Card>

                {/* Enhanced QuickBooks Connection Component */}
                <QuickBooksConnection
                  qboConnected={qboConnected}
                  qboLoading={qboLoading}
                  qboStatus={qboStatus}
                  company={company}
                  onConnect={handleQBOConnect}
                  onDisconnect={handleQBODisconnect}
                  onRefreshStatus={fetchQBOStatus}
                />

                {/* Uploadable Sections Component */}
                <UploadableSection
                  companyFiles={company?.companyFiles}
                  qboConnected={qboConnected}
                  qboLoading={qboLoading}
                  initialLoading={initialLoading}
                  onUpload={handleOpenModal}
                  onSync={handleSync}
                  syncLoading={syncLoading}
                  companyData={company}
                />
              </Box>
            ) : activeTab === "reports" ? (
              <Reports
                setActiveTab={setActiveTab}
                companyId={id}
                companyFiles={company?.companyFiles}
              />
            ) : activeTab === "report-settings" ? (
              <ReportSettings companyId={id} />
            ) : null}
          </Box>

          {/* Render Reports Tab Content */}
          {/* {activeTab === "reports" && (
            <Reports
              setActiveTab={setActiveTab}
              companyId={id}
              companyFiles={company?.companyFiles}
            />

            
          )} */}
        </Box>
      </Box>

      {showModal && (
        <CSVUploadModal
          onClose={() => setShowModal(false)}
          onUpload={handleUpload}
          companyId={id}
          type={fileTypeSection}
          onSuccess={fetchData}
        />
      )}

      <EditCompany
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        companyData={company}
        onSave={handleSaveCompany}
      />
    </>
  );
}

export default CompanyDetail;
